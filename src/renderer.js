// Renderer process for Instant TeX
console.log('Instant TeX renderer loaded');

// Import CodeMirror modules
const { EditorView, basicSetup } = require('codemirror');
const { EditorState } = require('@codemirror/state');
const { oneDark } = require('@codemirror/theme-one-dark');
const { javascript } = require('@codemirror/lang-javascript');
const { searchKeymap, highlightSelectionMatches } = require('@codemirror/search');
const { keymap } = require('@codemirror/view');
const { selectNextOccurrence } = require('@codemirror/search');
const { indentWithTab } = require('@codemirror/commands');
const { latex } = require('./latex-mode');

// Global variables
let editorView = null;
let debounceTimer = null;

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing app...');

    // Initialize components
    initializeResizer();
    initializeEditor();
    initializePreview();

    // The initial preview will be set when the editor loads with the initial document
});

// Initialize panel resizer
function initializeResizer() {
    const resizer = document.querySelector('.resizer');
    const editorPanel = document.querySelector('.editor-panel');
    const previewPanel = document.querySelector('.preview-panel');
    const container = document.querySelector('.container');
    
    let isResizing = false;
    
    resizer.addEventListener('mousedown', (e) => {
        isResizing = true;
        document.body.style.cursor = 'col-resize';
        document.body.style.userSelect = 'none';
    });
    
    document.addEventListener('mousemove', (e) => {
        if (!isResizing) return;
        
        const containerRect = container.getBoundingClientRect();
        const newLeftWidth = e.clientX - containerRect.left;
        const containerWidth = containerRect.width;
        
        // Ensure minimum widths
        const minWidth = 300;
        const maxLeftWidth = containerWidth - minWidth - 4; // 4px for resizer
        
        if (newLeftWidth >= minWidth && newLeftWidth <= maxLeftWidth) {
            const leftPercent = (newLeftWidth / containerWidth) * 100;
            const rightPercent = 100 - leftPercent - (4 / containerWidth * 100); // Account for resizer width
            
            editorPanel.style.flex = `0 0 ${leftPercent}%`;
            previewPanel.style.flex = `0 0 ${rightPercent}%`;
        }
    });
    
    document.addEventListener('mouseup', () => {
        if (isResizing) {
            isResizing = false;
            document.body.style.cursor = '';
            document.body.style.userSelect = '';
        }
    });
}

// Initialize CodeMirror editor
function initializeEditor() {
    console.log('Initializing CodeMirror editor...');

    const editorContainer = document.getElementById('editor-container');

    // Create initial document content - simple math formula
    const initialDoc = `\\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}`;

    // Create editor state with extensions
    const state = EditorState.create({
        doc: initialDoc,
        extensions: [
            basicSetup,
            oneDark,
            // LaTeX syntax highlighting
            latex(),
            // Highlight selection matches (word highlighting)
            highlightSelectionMatches(),
            // Custom keymap for Cmd+D (select next occurrence) and other shortcuts
            keymap.of([
                {
                    key: 'Cmd-d',
                    mac: 'Cmd-d',
                    run: selectNextOccurrence,
                    preventDefault: true
                },
                // Tab for indentation
                indentWithTab,
                // Add other useful shortcuts
                {
                    key: 'Cmd-/',
                    mac: 'Cmd-/',
                    run: (view) => {
                        // Toggle comment (add % at beginning of line)
                        const { state } = view;
                        const changes = [];

                        for (const range of state.selection.ranges) {
                            const line = state.doc.lineAt(range.from);
                            const lineText = line.text;

                            if (lineText.trim().startsWith('%')) {
                                // Remove comment
                                const match = lineText.match(/^(\s*)%\s?/);
                                if (match) {
                                    changes.push({
                                        from: line.from,
                                        to: line.from + match[0].length,
                                        insert: match[1]
                                    });
                                }
                            } else {
                                // Add comment
                                const indent = lineText.match(/^\s*/)[0];
                                changes.push({
                                    from: line.from,
                                    to: line.from + indent.length,
                                    insert: indent + '% '
                                });
                            }
                        }

                        if (changes.length > 0) {
                            view.dispatch({ changes });
                        }
                        return true;
                    }
                }
            ]),
            // Real-time preview updates
            EditorView.updateListener.of((update) => {
                if (update.docChanged) {
                    // Debounce updates for performance
                    clearTimeout(debounceTimer);
                    debounceTimer = setTimeout(() => {
                        updatePreview(update.state.doc.toString());
                    }, 300);
                }
            }),
            // Custom theme and styling
            EditorView.theme({
                '&': {
                    height: '100%'
                },
                '.cm-scroller': {
                    fontFamily: 'SF Mono, Monaco, Cascadia Code, Roboto Mono, Consolas, Courier New, monospace',
                    fontSize: '14px',
                    lineHeight: '1.5'
                },
                // Style for highlighted selection matches
                '.cm-selectionMatch': {
                    backgroundColor: '#3392FF44'
                },
                // Auto-indentation styling
                '.cm-line': {
                    paddingLeft: '4px'
                },
                // LaTeX syntax highlighting colors
                '.cm-content .tok-keyword': {
                    color: '#569cd6 !important'
                },
                '.cm-content .tok-string': {
                    color: '#ce9178 !important'
                },
                '.cm-content .tok-comment': {
                    color: '#6a9955 !important',
                    fontStyle: 'italic'
                },
                '.cm-content .tok-bracket': {
                    color: '#ffd700 !important'
                },
                '.cm-content .tok-number': {
                    color: '#b5cea8 !important'
                },
                '.cm-content .tok-operator': {
                    color: '#d4d4d4 !important'
                },
                // Alternative selectors for different token naming
                '.cm-keyword': {
                    color: '#569cd6 !important'
                },
                '.cm-string': {
                    color: '#ce9178 !important'
                },
                '.cm-comment': {
                    color: '#6a9955 !important',
                    fontStyle: 'italic'
                },
                '.cm-bracket': {
                    color: '#ffd700 !important'
                },
                '.cm-number': {
                    color: '#b5cea8 !important'
                },
                '.cm-operator': {
                    color: '#d4d4d4 !important'
                }
            })
        ]
    });

    // Create editor view
    editorView = new EditorView({
        state,
        parent: editorContainer
    });

    console.log('CodeMirror editor initialized');
}

// Initialize preview panel
function initializePreview() {
    console.log('Preview panel initialized');
}

// Update preview with LaTeX math content
function updatePreview(latexContent) {
    const previewContent = document.querySelector('.preview-content');

    try {
        // Clean the content - remove comments but preserve line breaks for formatting
        let cleanContent = latexContent
            .split('\n')
            .map(line => line.replace(/%.*$/, '').trim()) // Remove comments
            .filter(line => line.length > 0) // Remove empty lines
            .join(' '); // Join with spaces to preserve math structure

        // If no content, show placeholder
        if (!cleanContent.trim()) {
            const htmlContent = `
                <div class="placeholder">
                    <h3>Welcome to Instant TeX!</h3>
                    <p>Start typing LaTeX math formulas in the editor...</p>
                    <div class="examples">
                        <h4>Examples:</h4>
                        <ul>
                            <li><code>a^2 + b^2 = c^2</code></li>
                            <li><code>\\frac{-b \\pm \\sqrt{b^2-4ac}}{2a}</code></li>
                            <li><code>\\int_0^\\infty e^{-x^2} dx</code></li>
                            <li><code>\\sum_{i=1}^n x_i</code></li>
                        </ul>
                    </div>
                    <div class="shortcuts">
                        <h4>Keyboard Shortcuts:</h4>
                        <ul>
                            <li><kbd>⌘D</kbd> - Select next occurrence</li>
                            <li><kbd>⌘/</kbd> - Toggle comment</li>
                            <li><kbd>Tab</kbd> - Indent</li>
                        </ul>
                    </div>
                </div>
            `;
            previewContent.innerHTML = htmlContent;
            return;
        }

        // Detect if content contains environment blocks (align, equation, etc.)
        const hasEnvironment = /\\begin\{(align|equation|gather|multline|split)\*?\}/.test(cleanContent);

        // Convert numbered environments to starred (unnumbered) versions to avoid equation numbers
        cleanContent = cleanContent
            .replace(/\\begin\{(align|equation|gather|multline)\}/g, '\\begin{$1*}')
            .replace(/\\end\{(align|equation|gather|multline)\}/g, '\\end{$1*}');

        try {
            let rendered;
            if (hasEnvironment) {
                // Render as display math for environments
                rendered = katex.renderToString(cleanContent, {
                    displayMode: true,
                    throwOnError: false,
                    errorColor: '#cc0000'
                });
            } else {
                // For simple expressions, try display mode first, fallback to inline
                try {
                    rendered = katex.renderToString(cleanContent, {
                        displayMode: true,
                        throwOnError: true
                    });
                } catch (e) {
                    // If display mode fails, try inline mode
                    rendered = katex.renderToString(cleanContent, {
                        displayMode: false,
                        throwOnError: false,
                        errorColor: '#cc0000'
                    });
                }
            }

            previewContent.innerHTML = `<div class="math-container">${rendered}</div>`;

        } catch (e) {
            // Show error with the problematic content
            previewContent.innerHTML = `
                <div class="math-error-line">
                    <div class="error-content">\\[${cleanContent}\\]</div>
                    <div class="error-message">Error: ${e.message}</div>
                </div>
            `;
        }

    } catch (error) {
        console.error('Preview update error:', error);
        previewContent.innerHTML = `<div class="error">Preview Error: ${error.message}</div>`;
    }
}
