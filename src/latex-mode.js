// Custom LaTeX syntax highlighting for CodeMirror 6
const { StreamLanguage, LanguageSupport, syntaxHighlighting, HighlightStyle } = require('@codemirror/language');
const { tags } = require('@lezer/highlight');

// Create a simple LaTeX language mode using StreamLanguage
const latexLanguage = StreamLanguage.define({
  name: 'latex',

  token(stream, state) {
    // <PERSON>le comments
    if (stream.match(/^%.*$/)) {
      return tags.lineComment;
    }

    // Handle LaTeX commands (backslash followed by letters)
    if (stream.match(/^\\[a-zA-Z]+\*?/)) {
      return tags.keyword;
    }

    // Handle special LaTeX commands (backslash followed by non-letter)
    if (stream.match(/^\\[^a-zA-Z\s]/)) {
      return tags.keyword;
    }

    // Handle math delimiters
    if (stream.match(/^\$\$?/)) {
      return tags.string;
    }

    // Handle braces
    if (stream.match(/^[{}]/)) {
      return tags.brace;
    }

    // Handle square brackets
    if (stream.match(/^[\[\]]/)) {
      return tags.squareBracket;
    }

    // Handle numbers
    if (stream.match(/^[0-9]+(\.[0-9]*)?/)) {
      return tags.number;
    }

    // Handle operators and special characters
    if (stream.match(/^[+\-*/=<>!&|^_]/)) {
      return tags.operator;
    }

    // Handle parentheses
    if (stream.match(/^[()]/)) {
      return tags.paren;
    }

    // Default: advance one character
    stream.next();
    return null;
  },

  languageData: {
    commentTokens: { line: '%' },
    indentOnInput: /^\s*\\(begin|end)\b/,
    wordChars: 'a-zA-Z\\'
  }
});

// Create a custom highlight style for LaTeX that works with dark themes
const latexHighlightStyle = HighlightStyle.define([
  { tag: tags.keyword, color: '#569cd6' },           // LaTeX commands - blue
  { tag: tags.lineComment, color: '#6a9955', fontStyle: 'italic' }, // Comments - green
  { tag: tags.string, color: '#ce9178' },            // Math delimiters - orange
  { tag: [tags.brace, tags.squareBracket, tags.paren], color: '#ffd700' }, // Brackets - gold
  { tag: tags.number, color: '#b5cea8' },            // Numbers - light green
  { tag: tags.operator, color: '#d4d4d4' },          // Operators - light gray
]);

// Export the language support with highlighting
function latex() {
  return new LanguageSupport(latexLanguage, [
    syntaxHighlighting(latexHighlightStyle)
  ]);
}

module.exports = { latex };
