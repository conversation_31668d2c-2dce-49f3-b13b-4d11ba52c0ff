// Custom LaTeX syntax highlighting for CodeMirror 6
const { StreamLanguage } = require('@codemirror/language');
const { LanguageSupport } = require('@codemirror/language');

// Create a simple LaTeX language mode using StreamLanguage
const latexLanguage = StreamLanguage.define({
  name: 'latex',

  token(stream, state) {
    // Handle comments
    if (stream.match(/^%.*$/)) {
      return 'comment';
    }

    // Handle LaTeX commands (backslash followed by letters)
    if (stream.match(/^\\[a-zA-Z]+\*?/)) {
      return 'keyword';
    }

    // Handle special LaTeX commands (backslash followed by non-letter)
    if (stream.match(/^\\[^a-zA-Z\s]/)) {
      return 'keyword';
    }

    // Handle math delimiters
    if (stream.match(/^\$\$?/)) {
      return 'string';
    }

    // Handle braces
    if (stream.match(/^[{}]/)) {
      return 'bracket';
    }

    // Handle square brackets
    if (stream.match(/^[\[\]]/)) {
      return 'bracket';
    }

    // Handle numbers
    if (stream.match(/^[0-9]+(\.[0-9]*)?/)) {
      return 'number';
    }

    // Handle operators and special characters
    if (stream.match(/^[+\-*/=<>!&|^_]/)) {
      return 'operator';
    }

    // Handle parentheses
    if (stream.match(/^[()]/)) {
      return 'bracket';
    }

    // Default: advance one character
    stream.next();
    return null;
  },

  languageData: {
    commentTokens: { line: '%' },
    indentOnInput: /^\s*\\(begin|end)\b/,
    wordChars: 'a-zA-Z\\'
  }
});

// Export the language support
function latex() {
  return new LanguageSupport(latexLanguage);
}

module.exports = { latex };
